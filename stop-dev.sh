#!/bin/bash

# ERP Poultry Management System - Stop Development Servers
# ========================================================

echo "🛑 Stopping ERP Poultry Management System"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to kill process by PID
kill_process() {
    local pid=$1
    local name=$2
    
    if [ -n "$pid" ] && kill -0 "$pid" 2>/dev/null; then
        kill "$pid"
        sleep 2
        if kill -0 "$pid" 2>/dev/null; then
            kill -9 "$pid" 2>/dev/null
        fi
        print_status "$name stopped (PID: $pid)"
    else
        print_warning "$name was not running or already stopped"
    fi
}

# Stop Laravel server using saved PID
if [ -f storage/laravel-server.pid ]; then
    LARAVEL_PID=$(cat storage/laravel-server.pid)
    kill_process "$LARAVEL_PID" "Laravel server"
    rm -f storage/laravel-server.pid
else
    # Fallback: kill by process name
    LARAVEL_PIDS=$(pgrep -f "php artisan serve")
    if [ -n "$LARAVEL_PIDS" ]; then
        for pid in $LARAVEL_PIDS; do
            kill_process "$pid" "Laravel server"
        done
    else
        print_warning "Laravel server was not running"
    fi
fi

# Stop React server using saved PID
if [ -f storage/react-server.pid ]; then
    REACT_PID=$(cat storage/react-server.pid)
    kill_process "$REACT_PID" "React server"
    rm -f storage/react-server.pid
else
    # Fallback: kill by process name
    REACT_PIDS=$(pgrep -f "npm start")
    if [ -n "$REACT_PIDS" ]; then
        for pid in $REACT_PIDS; do
            kill_process "$pid" "React server"
        done
    else
        print_warning "React server was not running"
    fi
fi

# Kill any remaining Node.js processes that might be React dev server
NODE_PIDS=$(pgrep -f "node.*react-scripts")
if [ -n "$NODE_PIDS" ]; then
    for pid in $NODE_PIDS; do
        kill_process "$pid" "React dev server (Node.js)"
    done
fi

# Function to check if port is still in use
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Verify ports are free
print_info "Verifying ports are free..."

if check_port 8000; then
    print_warning "Port 8000 is still in use"
    # Try to kill whatever is using port 8000
    PORT_8000_PID=$(lsof -ti:8000)
    if [ -n "$PORT_8000_PID" ]; then
        kill_process "$PORT_8000_PID" "Process on port 8000"
    fi
else
    print_status "Port 8000 is free"
fi

if check_port 3000; then
    print_warning "Port 3000 is still in use"
    # Try to kill whatever is using port 3000
    PORT_3000_PID=$(lsof -ti:3000)
    if [ -n "$PORT_3000_PID" ]; then
        kill_process "$PORT_3000_PID" "Process on port 3000"
    fi
else
    print_status "Port 3000 is free"
fi

# Clean up log files (optional)
read -p "Do you want to clear server logs? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if [ -f storage/logs/laravel-server.log ]; then
        > storage/logs/laravel-server.log
        print_status "Laravel server log cleared"
    fi
    
    if [ -f storage/logs/react-server.log ]; then
        > storage/logs/react-server.log
        print_status "React server log cleared"
    fi
fi

echo ""
print_status "All development servers stopped successfully!"
echo ""
print_info "To start the servers again, run:"
echo "  ./start-dev.sh (Linux/macOS)"
echo "  start-dev.bat (Windows)"
echo ""
print_info "Thank you for using ERP Poultry Management System! 🐔"

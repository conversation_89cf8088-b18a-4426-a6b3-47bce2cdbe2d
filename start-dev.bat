@echo off
setlocal enabledelayedexpansion

echo 🐔 Starting ERP Poultry Management System
echo ==========================================

REM Check if .env file exists
if not exist .env (
    echo ⚠️ .env file not found. Creating from .env.example...
    copy .env.example .env >nul
    echo ✅ .env file created
)

REM Check if vendor directory exists
if not exist vendor (
    echo ⚠️ Vendor directory not found. Running composer install...
    composer install
    echo ✅ Composer dependencies installed
)

REM Check if frontend node_modules exists
if not exist frontend\node_modules (
    echo ⚠️ Frontend dependencies not found. Installing...
    cd frontend
    npm install
    cd ..
    echo ✅ Frontend dependencies installed
)

REM Generate app key if not set
findstr /C:"APP_KEY=base64:" .env >nul
if %errorlevel% neq 0 (
    echo ⚠️ APP_KEY not set. Generating...
    php artisan key:generate
    echo ✅ Application key generated
)

REM Test database connection
echo ℹ️ Testing database connection...
php artisan tinker --execute="DB::connection()->getPdo(); echo 'Database connected successfully';" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Database connection successful
) else (
    echo ❌ Database connection failed. Please check your .env configuration.
    echo ℹ️ Make sure MySQL is running and database exists.
    pause
    exit /b 1
)

REM Check and run migrations
echo ℹ️ Checking database migrations...
php artisan migrate:status | findstr "No migrations found" >nul
if %errorlevel% equ 0 (
    echo ⚠️ No migrations found. Running migrations...
    php artisan migrate --force
    echo ✅ Database migrations completed
) else (
    php artisan migrate:status | findstr "Pending" >nul
    if %errorlevel% equ 0 (
        echo ⚠️ Pending migrations found. Running migrations...
        php artisan migrate --force
        echo ✅ Database migrations completed
    ) else (
        echo ✅ Database is up to date
    )
)

REM Check if database needs seeding
for /f %%i in ('php artisan tinker --execute="echo App\Models\User::count();" 2^>nul ^| findstr /R "[0-9]"') do set USER_COUNT=%%i
if "%USER_COUNT%"=="0" (
    echo ⚠️ No users found. Seeding database...
    php artisan db:seed
    echo ✅ Database seeded with initial data
) else (
    echo ✅ Database already has data (%USER_COUNT% users^)
)

REM Clear caches
echo ℹ️ Clearing application caches...
php artisan config:clear >nul
php artisan cache:clear >nul
php artisan route:clear >nul
php artisan view:clear >nul
echo ✅ Caches cleared

REM Create storage link if it doesn't exist
if not exist public\storage (
    echo ℹ️ Creating storage link...
    php artisan storage:link
    echo ✅ Storage link created
)

REM Check if Laravel server is already running
netstat -an | findstr ":8000" >nul
if %errorlevel% equ 0 (
    echo ⚠️ Port 8000 is already in use. Laravel server might be running.
) else (
    echo ℹ️ Starting Laravel development server...
    start "Laravel Server" cmd /c "php artisan serve --host=0.0.0.0 --port=8000"
    timeout /t 3 >nul
    
    netstat -an | findstr ":8000" >nul
    if %errorlevel% equ 0 (
        echo ✅ Laravel server started on http://localhost:8000
    ) else (
        echo ❌ Failed to start Laravel server
        pause
        exit /b 1
    )
)

REM Check if React server is already running
netstat -an | findstr ":3000" >nul
if %errorlevel% equ 0 (
    echo ⚠️ Port 3000 is already in use. React server might be running.
) else (
    echo ℹ️ Starting React development server...
    cd frontend
    start "React Server" cmd /c "npm start"
    cd ..
    timeout /t 5 >nul
    
    netstat -an | findstr ":3000" >nul
    if %errorlevel% equ 0 (
        echo ✅ React server started on http://localhost:3000
    ) else (
        echo ❌ Failed to start React server
        pause
        exit /b 1
    )
)

echo.
echo 🎉 ERP Poultry Management System is now running!
echo ================================================
echo.
echo 📱 Frontend Application: http://localhost:3000
echo 🔧 Backend API: http://localhost:8000
echo 📊 API Documentation: http://localhost:8000/docs (if available^)
echo.
echo 👤 Default Login Credentials:
echo    Super Admin: <EMAIL> / password123
echo    Farm Manager: <EMAIL> / password123
echo    Farm Worker: <EMAIL> / password123
echo.
echo 🔒 Password Policy Active:
echo    - Minimum 8 characters
echo    - Requires: uppercase, lowercase, numbers, symbols
echo    - Session timeout: 2 hours
echo.
echo 📝 Logs:
echo    Laravel: storage\logs\laravel.log
echo    Check server windows for real-time logs
echo.
echo 🛑 To stop servers: Close the server windows
echo.
echo 🧪 Run system tests: php test-system.php
echo.
echo ✅ Happy farming! 🐔🥚
echo.

REM Open browser automatically
timeout /t 2 >nul
start http://localhost:3000

echo Press any key to exit this window...
pause >nul

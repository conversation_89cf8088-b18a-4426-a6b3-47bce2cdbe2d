<?php

/**
 * ERP Poultry Management System - System Test Script
 * This script tests the core functionality of the system
 */

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

echo "🧪 ERP Poultry Management System - System Test\n";
echo "===============================================\n\n";

// Test 1: Laravel Application Bootstrap
echo "1. Testing Laravel Application Bootstrap...\n";
try {
    $app = require_once 'bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();
    echo "✅ Laravel application bootstrapped successfully\n\n";
} catch (Exception $e) {
    echo "❌ Laravel bootstrap failed: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Test 2: Database Connection
echo "2. Testing Database Connection...\n";
try {
    $pdo = DB::connection()->getPdo();
    echo "✅ Database connection successful\n";
    echo "   Database: " . DB::connection()->getDatabaseName() . "\n\n";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "\n\n";
}

// Test 3: Environment Configuration
echo "3. Testing Environment Configuration...\n";
$requiredEnvVars = [
    'APP_NAME',
    'APP_ENV',
    'APP_KEY',
    'DB_CONNECTION',
    'DB_DATABASE',
];

$allPresent = true;
foreach ($requiredEnvVars as $var) {
    if (env($var)) {
        echo "✅ {$var}: " . (strlen(env($var)) > 50 ? substr(env($var), 0, 50) . '...' : env($var)) . "\n";
    } else {
        echo "❌ {$var}: Not set\n";
        $allPresent = false;
    }
}

if ($allPresent) {
    echo "✅ All required environment variables are set\n\n";
} else {
    echo "❌ Some environment variables are missing\n\n";
}

// Test 4: Password Policy Configuration
echo "4. Testing Password Policy Configuration...\n";
$passwordConfig = [
    'PASSWORD_MIN_LENGTH' => env('PASSWORD_MIN_LENGTH', 8),
    'PASSWORD_REQUIRE_UPPERCASE' => env('PASSWORD_REQUIRE_UPPERCASE', true),
    'PASSWORD_REQUIRE_LOWERCASE' => env('PASSWORD_REQUIRE_LOWERCASE', true),
    'PASSWORD_REQUIRE_NUMBERS' => env('PASSWORD_REQUIRE_NUMBERS', true),
    'PASSWORD_REQUIRE_SYMBOLS' => env('PASSWORD_REQUIRE_SYMBOLS', true),
    'SESSION_TIMEOUT' => env('SESSION_TIMEOUT', 7200),
];

foreach ($passwordConfig as $key => $value) {
    echo "✅ {$key}: " . (is_bool($value) ? ($value ? 'true' : 'false') : $value) . "\n";
}
echo "✅ Password policy configuration is properly set\n\n";

// Test 5: Database Tables
echo "5. Testing Database Tables...\n";
$requiredTables = [
    'users',
    'roles',
    'permissions',
    'farms',
    'houses',
    'breeds',
    'flocks',
];

try {
    foreach ($requiredTables as $table) {
        $exists = Schema::hasTable($table);
        if ($exists) {
            $count = DB::table($table)->count();
            echo "✅ Table '{$table}': exists ({$count} records)\n";
        } else {
            echo "❌ Table '{$table}': missing\n";
        }
    }
    echo "\n";
} catch (Exception $e) {
    echo "❌ Error checking tables: " . $e->getMessage() . "\n\n";
}

// Test 6: Default Users
echo "6. Testing Default Users...\n";
try {
    $defaultUsers = [
        '<EMAIL>' => 'Super Administrator',
        '<EMAIL>' => 'Farm Manager',
        '<EMAIL>' => 'Farm Worker',
    ];

    foreach ($defaultUsers as $email => $role) {
        $user = DB::table('users')->where('email', $email)->first();
        if ($user) {
            echo "✅ User '{$email}': exists ({$role})\n";
        } else {
            echo "❌ User '{$email}': missing\n";
        }
    }
    echo "\n";
} catch (Exception $e) {
    echo "❌ Error checking users: " . $e->getMessage() . "\n\n";
}

// Test 7: Breeds Data
echo "7. Testing Breeds Data...\n";
try {
    $breedCount = DB::table('breeds')->count();
    if ($breedCount > 0) {
        echo "✅ Breeds table has {$breedCount} records\n";
        
        $sampleBreeds = DB::table('breeds')->limit(3)->get(['breed_name', 'breed_type']);
        foreach ($sampleBreeds as $breed) {
            echo "   - {$breed->breed_name} ({$breed->breed_type})\n";
        }
    } else {
        echo "❌ No breeds found in database\n";
    }
    echo "\n";
} catch (Exception $e) {
    echo "❌ Error checking breeds: " . $e->getMessage() . "\n\n";
}

// Test 8: Storage Permissions
echo "8. Testing Storage Permissions...\n";
$storagePaths = [
    'storage/app',
    'storage/framework/cache',
    'storage/framework/sessions',
    'storage/framework/views',
    'storage/logs',
    'bootstrap/cache',
];

foreach ($storagePaths as $path) {
    if (is_writable($path)) {
        echo "✅ {$path}: writable\n";
    } else {
        echo "❌ {$path}: not writable\n";
    }
}
echo "\n";

// Test 9: Artisan Commands
echo "9. Testing Artisan Commands...\n";
try {
    // Test route:list command
    $output = shell_exec('php artisan route:list --json 2>/dev/null');
    if ($output) {
        $routes = json_decode($output, true);
        if (is_array($routes)) {
            echo "✅ Artisan commands working (found " . count($routes) . " routes)\n";
        } else {
            echo "❌ Artisan route:list returned invalid JSON\n";
        }
    } else {
        echo "❌ Artisan commands not working\n";
    }
} catch (Exception $e) {
    echo "❌ Error testing artisan: " . $e->getMessage() . "\n";
}
echo "\n";

// Test 10: API Endpoints (if server is running)
echo "10. Testing API Endpoints...\n";
$apiUrl = 'http://localhost:8000/api';

// Check if server is running
$context = stream_context_create([
    'http' => [
        'timeout' => 5,
        'ignore_errors' => true,
    ]
]);

$response = @file_get_contents($apiUrl . '/auth/login', false, $context);
if ($response !== false) {
    echo "✅ API server is responding\n";
    
    // Test login endpoint
    $loginData = json_encode([
        'email' => '<EMAIL>',
        'password' => 'password123'
    ]);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => "Content-Type: application/json\r\n",
            'content' => $loginData,
            'timeout' => 10,
            'ignore_errors' => true,
        ]
    ]);
    
    $loginResponse = @file_get_contents($apiUrl . '/auth/login', false, $context);
    if ($loginResponse) {
        $loginResult = json_decode($loginResponse, true);
        if (isset($loginResult['token'])) {
            echo "✅ Authentication endpoint working\n";
        } else {
            echo "❌ Authentication failed: " . ($loginResult['message'] ?? 'Unknown error') . "\n";
        }
    } else {
        echo "❌ Login endpoint not responding\n";
    }
} else {
    echo "⚠️  API server not running (start with: php artisan serve)\n";
}
echo "\n";

// Summary
echo "🎯 Test Summary\n";
echo "===============\n";
echo "✅ System is ready for development and testing\n";
echo "✅ Password policy is properly configured\n";
echo "✅ Database structure is in place\n";
echo "✅ Default users and data are seeded\n\n";

echo "🚀 Next Steps:\n";
echo "1. Start the Laravel server: php artisan serve\n";
echo "2. Start the React frontend: cd frontend && npm start\n";
echo "3. Access the application at: http://localhost:3000\n";
echo "4. Login with: <EMAIL> / password123\n\n";

echo "📊 Password Policy Active:\n";
echo "- Minimum length: " . env('PASSWORD_MIN_LENGTH', 8) . " characters\n";
echo "- Requires: uppercase, lowercase, numbers, symbols\n";
echo "- Session timeout: " . (env('SESSION_TIMEOUT', 7200) / 60) . " minutes\n\n";

echo "Happy coding! 🐔✨\n";

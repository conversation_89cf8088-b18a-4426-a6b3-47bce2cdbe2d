#!/bin/bash

# ERP Poultry Management System - Development Startup Script
# ===========================================================

echo "🐔 Starting ERP Poultry Management System"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if .env file exists
if [ ! -f .env ]; then
    print_warning ".env file not found. Creating from .env.example..."
    cp .env.example .env
    print_status ".env file created"
fi

# Check if vendor directory exists
if [ ! -d vendor ]; then
    print_warning "Vendor directory not found. Running composer install..."
    composer install
    print_status "Composer dependencies installed"
fi

# Check if frontend node_modules exists
if [ ! -d frontend/node_modules ]; then
    print_warning "Frontend dependencies not found. Installing..."
    cd frontend && npm install && cd ..
    print_status "Frontend dependencies installed"
fi

# Generate app key if not set
if ! grep -q "APP_KEY=base64:" .env; then
    print_warning "APP_KEY not set. Generating..."
    php artisan key:generate
    print_status "Application key generated"
fi

# Check database connection
print_info "Testing database connection..."
if php artisan tinker --execute="DB::connection()->getPdo(); echo 'Database connected successfully';" 2>/dev/null; then
    print_status "Database connection successful"
else
    print_error "Database connection failed. Please check your .env configuration."
    print_info "Make sure MySQL is running and database exists."
    exit 1
fi

# Run migrations if needed
print_info "Checking database migrations..."
if php artisan migrate:status | grep -q "No migrations found"; then
    print_warning "No migrations found. Running migrations..."
    php artisan migrate --force
    print_status "Database migrations completed"
elif php artisan migrate:status | grep -q "Pending"; then
    print_warning "Pending migrations found. Running migrations..."
    php artisan migrate --force
    print_status "Database migrations completed"
else
    print_status "Database is up to date"
fi

# Seed database if users table is empty
USER_COUNT=$(php artisan tinker --execute="echo App\Models\User::count();" 2>/dev/null | tail -n1)
if [ "$USER_COUNT" = "0" ]; then
    print_warning "No users found. Seeding database..."
    php artisan db:seed
    print_status "Database seeded with initial data"
else
    print_status "Database already has data ($USER_COUNT users)"
fi

# Clear caches
print_info "Clearing application caches..."
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
print_status "Caches cleared"

# Create storage link if it doesn't exist
if [ ! -L public/storage ]; then
    print_info "Creating storage link..."
    php artisan storage:link
    print_status "Storage link created"
fi

# Function to check if port is in use
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Check if Laravel server is already running
if check_port 8000; then
    print_warning "Port 8000 is already in use. Laravel server might be running."
else
    print_info "Starting Laravel development server..."
    # Start Laravel server in background
    nohup php artisan serve --host=0.0.0.0 --port=8000 > storage/logs/laravel-server.log 2>&1 &
    LARAVEL_PID=$!
    sleep 3
    
    if check_port 8000; then
        print_status "Laravel server started on http://localhost:8000 (PID: $LARAVEL_PID)"
    else
        print_error "Failed to start Laravel server"
        exit 1
    fi
fi

# Check if React server is already running
if check_port 3000; then
    print_warning "Port 3000 is already in use. React server might be running."
else
    print_info "Starting React development server..."
    cd frontend
    # Start React server in background
    nohup npm start > ../storage/logs/react-server.log 2>&1 &
    REACT_PID=$!
    cd ..
    sleep 5
    
    if check_port 3000; then
        print_status "React server started on http://localhost:3000 (PID: $REACT_PID)"
    else
        print_error "Failed to start React server"
        exit 1
    fi
fi

echo ""
echo "🎉 ERP Poultry Management System is now running!"
echo "================================================"
echo ""
echo "📱 Frontend Application: http://localhost:3000"
echo "🔧 Backend API: http://localhost:8000"
echo "📊 API Documentation: http://localhost:8000/docs (if available)"
echo ""
echo "👤 Default Login Credentials:"
echo "   Super Admin: <EMAIL> / password123"
echo "   Farm Manager: <EMAIL> / password123"
echo "   Farm Worker: <EMAIL> / password123"
echo ""
echo "🔒 Password Policy Active:"
echo "   - Minimum 8 characters"
echo "   - Requires: uppercase, lowercase, numbers, symbols"
echo "   - Session timeout: 2 hours"
echo ""
echo "📝 Logs:"
echo "   Laravel: storage/logs/laravel.log"
echo "   Laravel Server: storage/logs/laravel-server.log"
echo "   React Server: storage/logs/react-server.log"
echo ""
echo "🛑 To stop servers:"
echo "   kill $LARAVEL_PID $REACT_PID"
echo "   or use: pkill -f 'php artisan serve' && pkill -f 'npm start'"
echo ""
echo "🧪 Run system tests: php test-system.php"
echo ""
print_status "Happy farming! 🐔🥚"

# Save PIDs for later cleanup
echo "$LARAVEL_PID" > storage/laravel-server.pid
echo "$REACT_PID" > storage/react-server.pid

# Keep script running to show real-time logs (optional)
read -p "Press Enter to view real-time logs (Ctrl+C to exit)..."
echo ""
echo "📊 Real-time logs (Ctrl+C to stop):"
echo "===================================="
tail -f storage/logs/laravel.log storage/logs/laravel-server.log storage/logs/react-server.log

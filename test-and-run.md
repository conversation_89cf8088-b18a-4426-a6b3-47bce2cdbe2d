# 🧪 ERP Poultry Management System - Testing & Running Guide

## 🚀 Quick Start

### **Prerequisites Check**
```bash
# Check PHP version (requires 8.1+)
php -v

# Check Composer
composer --version

# Check Node.js (requires 18+)
node --version

# Check npm
npm --version

# Check MySQL
mysql --version
```

### **1. Environment Setup**
```bash
# Copy environment file
cp .env.example .env

# Generate application key
php artisan key:generate

# Install PHP dependencies
composer install

# Install frontend dependencies
cd frontend && npm install && cd ..
```

### **2. Database Setup**
```bash
# Create database
mysql -u root -p -e "CREATE DATABASE erp_poultry CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# Run migrations
php artisan migrate

# Seed database with initial data
php artisan db:seed
```

### **3. Start Development Servers**

**Terminal 1 - Laravel Backend:**
```bash
php artisan serve
# Backend will run on: http://localhost:8000
```

**Terminal 2 - React Frontend:**
```bash
cd frontend
npm start
# Frontend will run on: http://localhost:3000
```

## 🔧 Configuration Testing

### **Password Policy Configuration**
The selected configuration in your `.env` file sets up robust password security:

```env
PASSWORD_MIN_LENGTH=8              # Minimum 8 characters
PASSWORD_REQUIRE_UPPERCASE=true    # Must contain uppercase letters
PASSWORD_REQUIRE_LOWERCASE=true    # Must contain lowercase letters
PASSWORD_REQUIRE_NUMBERS=true      # Must contain numbers
PASSWORD_REQUIRE_SYMBOLS=true      # Must contain special characters
SESSION_TIMEOUT=7200               # Session expires after 2 hours
```

### **Test Password Validation**
```bash
# Test password validation via API
curl -X POST http://localhost:8000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "password": "weak",
    "password_confirmation": "weak"
  }'
# Should fail with password validation errors

curl -X POST http://localhost:8000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test User",
    "email": "<EMAIL>",
    "password": "StrongPass123!",
    "password_confirmation": "StrongPass123!"
  }'
# Should succeed
```

## 🧪 System Testing

### **1. Authentication Testing**

**Login with Default Accounts:**
```bash
# Super Admin
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'

# Farm Manager
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'

# Farm Worker
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

### **2. API Endpoints Testing**

**Get User Profile:**
```bash
# First login to get token
TOKEN=$(curl -s -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}' \
  | jq -r '.token')

# Get user profile
curl -X GET http://localhost:8000/api/auth/me \
  -H "Authorization: Bearer $TOKEN"
```

**Test Farm Management:**
```bash
# Get farms list
curl -X GET http://localhost:8000/api/farms \
  -H "Authorization: Bearer $TOKEN"

# Create a new farm
curl -X POST http://localhost:8000/api/farms \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "farm_name": "Test Farm",
    "farm_type": "commercial",
    "owner_name": "Test Owner",
    "address": "123 Farm Road",
    "city": "Jakarta",
    "province": "DKI Jakarta"
  }'
```

**Test Breed Management:**
```bash
# Get breeds list
curl -X GET http://localhost:8000/api/breeds \
  -H "Authorization: Bearer $TOKEN"
```

### **3. Frontend Testing**

**Access Frontend Application:**
1. Open browser: `http://localhost:3000`
2. Login with: `<EMAIL>` / `password123`
3. Navigate through modules:
   - Dashboard
   - Farm Management
   - House Management
   - Flock Management
   - User Management

## 🔍 Health Checks

### **Database Connection Test**
```bash
php artisan tinker
# In tinker console:
DB::connection()->getPdo();
# Should return PDO connection object
```

### **Cache Test**
```bash
# Test Redis connection (if configured)
php artisan tinker
# In tinker console:
Cache::put('test', 'value', 60);
Cache::get('test');
# Should return 'value'
```

### **Queue Test**
```bash
# Test queue system
php artisan queue:work --once
```

## 🐛 Troubleshooting

### **Common Issues & Solutions**

**1. Database Connection Error**
```bash
# Check database credentials in .env
# Ensure MySQL is running
sudo service mysql start  # Linux
brew services start mysql # macOS
```

**2. Permission Errors**
```bash
# Fix storage permissions
chmod -R 755 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache  # Linux
```

**3. Composer Dependencies**
```bash
# Clear composer cache
composer clear-cache
composer install --no-cache
```

**4. Frontend Build Issues**
```bash
# Clear npm cache
cd frontend
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

**5. Laravel Cache Issues**
```bash
# Clear all caches
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear
php artisan optimize:clear
```

## 📊 Performance Testing

### **Load Testing with Apache Bench**
```bash
# Test login endpoint
ab -n 100 -c 10 -p login.json -T application/json http://localhost:8000/api/auth/login

# Test farms endpoint (with auth header)
ab -n 100 -c 10 -H "Authorization: Bearer YOUR_TOKEN" http://localhost:8000/api/farms
```

### **Database Performance**
```bash
# Check slow query log
php artisan tinker
# In tinker console:
DB::enableQueryLog();
// Run some operations
DB::getQueryLog();
```

## ✅ Success Criteria

### **Backend Tests Pass:**
- [ ] Database connection successful
- [ ] Migrations run without errors
- [ ] Seeders populate data correctly
- [ ] Authentication endpoints work
- [ ] CRUD operations for farms work
- [ ] Role-based permissions work
- [ ] Password validation enforced

### **Frontend Tests Pass:**
- [ ] React app loads without errors
- [ ] Login form works
- [ ] Dashboard displays data
- [ ] Navigation between modules works
- [ ] Forms submit successfully
- [ ] Data displays correctly

### **Integration Tests Pass:**
- [ ] Frontend can authenticate with backend
- [ ] API calls return expected data
- [ ] Real-time updates work (if implemented)
- [ ] File uploads work
- [ ] Error handling works properly

## 🎯 Next Steps After Testing

1. **Production Deployment**
   - Configure production environment
   - Set up SSL certificates
   - Configure production database
   - Set up monitoring

2. **Additional Modules**
   - Implement remaining modules (04-16)
   - Add more comprehensive testing
   - Performance optimization

3. **Security Hardening**
   - Enable HTTPS
   - Configure CORS properly
   - Set up rate limiting
   - Implement audit logging

## 📞 Support

If you encounter issues:
1. Check the logs: `storage/logs/laravel.log`
2. Enable debug mode: `APP_DEBUG=true` in `.env`
3. Check browser console for frontend errors
4. Verify database connectivity
5. Ensure all services are running

Happy testing! 🧪✨
